# 中国象棋联机游戏

这是一个支持局域网和远程联机的中国象棋游戏，具有完整的棋子移动规则和实时同步功能。

## 功能特点

- **单人游戏**: 本地双人对战
- **局域网联机**: 支持局域网内多人联机对战
- **远程联机**: 支持通过GitHub Codespaces进行远程联机对战
- **实时同步**: 游戏状态和移动实时同步
- **连接状态显示**: 右上角显示连接状态
- **完整的象棋规则**: 包含所有棋子的移动和吃子规则

## 如何启动服务器

1. 确保已安装 Node.js
2. 在项目目录下运行以下命令：

```bash
# 安装依赖
npm install

# 启动服务器
npm start
# 或者
node server.js
```

3. 服务器启动后会显示访问地址：
   - 本机访问: `http://localhost:3000`
   - 局域网访问: `http://你的IP:3000`
   - GitHub Codespaces访问: `https://your-codespace-name-3000.preview.app.github.dev`

## 远程联机部署指南

### 使用GitHub Codespaces部署

1. **Fork这个仓库到你的GitHub账户**

2. **创建Codespace**：
   - 在GitHub仓库页面点击绿色的"Code"按钮
   - 选择"Codespaces"标签
   - 点击"Create codespace on main"

3. **自动部署**：
   - Codespace创建后会自动安装依赖并启动服务器
   - 查看终端输出获取WebSocket连接地址

4. **获取服务器地址**：
   - 服务器启动后，终端会显示类似这样的地址：
   ```
   GitHub Codespaces访问: https://your-codespace-name-3000.preview.app.github.dev
   WebSocket地址: wss://your-codespace-name-3000.preview.app.github.dev
   ```

5. **分享给朋友**：
   - 将WebSocket地址分享给想要联机的朋友
   - 朋友在游戏中选择"远程联机"并输入这个地址

### 手动部署到其他平台

如果你想部署到其他云平台（如Heroku、Railway等），只需：

1. 确保平台支持WebSocket
2. 设置环境变量 `PORT`（如果需要）
3. 运行 `npm install && npm start`

## 如何使用远程联机

1. **服务器部署者**：
   - 按照上面的部署指南在GitHub Codespaces部署服务器
   - 获取WebSocket地址并分享给朋友

2. **游戏玩家**：
   - 打开游戏，点击"远程联机"
   - 输入服务器提供的WebSocket地址
   - 等待连接成功，开始游戏

## 如何使用局域网联机

### 服务器端（主机）
1. 启动服务器（按上述步骤）
2. 记下终端显示的两个网址
3. 在浏览器中打开本机访问地址
4. 点击"局域网联机"按钮
5. 等待其他玩家连接

### 客户端（其他玩家）
1. 确保与主机在同一局域网内
2. 使用主机提供的局域网访问地址
3. 在浏览器中打开该地址
4. 点击"局域网联机"按钮
5. 连接成功后开始游戏

## 连接状态说明

- **正在连接...**: 橙色，正在尝试连接服务器
- **已连接**: 绿色，已成功连接并可以开始游戏
- **连接断开**: 红色，连接已断开，会自动尝试重连

## 游戏流程

1. 两名玩家都连接后，进入棋子初始化阶段
2. 双方分别摆放自己的棋子
3. 双方都完成初始化后，自动进入游戏阶段
4. 轮流移动棋子，所有操作实时同步

## 技术特点

- 使用 WebSocket 实现实时通信
- 自动房间管理，无需手动创建房间
- 断线重连机制
- 游戏状态完全同步

## 注意事项

- 确保防火墙允许 3000 端口的访问
- 局域网内的所有设备都可以连接
- 支持无限制的玩家数量（但每个房间最多2人）
- 游戏界面和玩法与单人模式完全相同

## 故障排除

如果连接失败，请检查：
1. 服务器是否正常运行
2. 网络连接是否正常
3. 防火墙设置是否正确
4. IP地址是否输入正确
