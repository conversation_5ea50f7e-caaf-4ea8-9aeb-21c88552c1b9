const WebSocket = require('ws');
const http = require('http');
const path = require('path');
const fs = require('fs');

// 创建HTTP服务器来提供静态文件
const server = http.createServer((req, res) => {
    // 添加CORS头部支持远程访问
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    // 处理预检请求
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }

    let filePath = '.' + req.url;
    if (filePath === './') {
        filePath = './index.html';
    }

    // 处理URL编码
    filePath = decodeURIComponent(filePath);

    const extname = String(path.extname(filePath)).toLowerCase();
    const mimeTypes = {
        '.html': 'text/html; charset=utf-8',
        '.js': 'text/javascript; charset=utf-8',
        '.css': 'text/css; charset=utf-8',
        '.json': 'application/json',
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.gif': 'image/gif',
        '.svg': 'image/svg+xml',
        '.wav': 'audio/wav',
        '.mp4': 'video/mp4',
        '.woff': 'application/font-woff',
        '.ttf': 'application/font-ttf',
        '.eot': 'application/vnd.ms-fontobject',
        '.otf': 'application/font-otf',
        '.wasm': 'application/wasm'
    };

    const contentType = mimeTypes[extname] || 'application/octet-stream';

    fs.readFile(filePath, (error, content) => {
        if (error) {
            if (error.code === 'ENOENT') {
                res.writeHead(404, { 'Content-Type': 'text/html; charset=utf-8' });
                res.end('404 Not Found', 'utf-8');
            } else {
                res.writeHead(500, { 'Content-Type': 'text/html; charset=utf-8' });
                res.end('Server Error: ' + error.code, 'utf-8');
            }
        } else {
            res.writeHead(200, { 'Content-Type': contentType });
            res.end(content);
        }
    });
});

// 创建WebSocket服务器
const wss = new WebSocket.Server({ server });

// 游戏房间管理
const gameRooms = new Map();
const connectedUsers = new Map(); // 存储所有连接的用户
const availableUserIds = new Set(); // 可用的用户ID集合
let maxUserId = 0; // 最大用户ID

// 获取最小可用的用户ID
function getNextAvailableUserId() {
    // 如果有可用的ID，使用最小的
    if (availableUserIds.size > 0) {
        const minId = Math.min(...availableUserIds);
        availableUserIds.delete(minId);
        return minId;
    }

    // 否则分配新的ID
    maxUserId++;
    return maxUserId;
}

// 释放用户ID
function releaseUserId(userId) {
    if (userId <= maxUserId) {
        availableUserIds.add(userId);
    }
}

class GameRoom {
    constructor(id, player1, player2) {
        this.id = id;
        this.players = [player1, player2];
        this.gameState = {
            boardState: {},
            gameMode: 'setup',
            redCompleted: false,
            blackCompleted: false,
            currentTurn: 'red', // 红方先手
            turnNumber: 1
        };

        // 随机分配红黑方
        const firstPlayerSide = Math.random() < 0.5 ? 'red' : 'black';
        player1.playerSide = firstPlayerSide;
        player2.playerSide = firstPlayerSide === 'red' ? 'black' : 'red';

        player1.roomId = this.id;
        player2.roomId = this.id;
        player1.inGame = true;
        player2.inGame = true;

        // 通知两个玩家游戏开始
        this.notifyGameStart();
    }

    notifyGameStart() {
        this.players.forEach(player => {
            if (player.readyState === WebSocket.OPEN) {
                player.send(JSON.stringify({
                    type: 'gameStarted',
                    playerId: player.playerId,
                    playerName: player.playerName,
                    playerSide: player.playerSide,
                    roomId: this.id,
                    opponent: {
                        playerId: this.getOpponent(player).playerId,
                        playerName: this.getOpponent(player).playerName,
                        playerSide: this.getOpponent(player).playerSide
                    }
                }));
            }
        });
    }

    getOpponent(player) {
        return this.players.find(p => p !== player);
    }

    removePlayer(ws) {
        const index = this.players.indexOf(ws);
        if (index !== -1) {
            // 通知对手游戏结束
            const opponent = this.getOpponent(ws);
            if (opponent && opponent.readyState === WebSocket.OPEN) {
                opponent.send(JSON.stringify({
                    type: 'opponentLeft',
                    message: '对手已离开游戏'
                }));
                opponent.inGame = false;
                opponent.roomId = null;
                opponent.playerSide = null;
            }

            ws.inGame = false;
            ws.roomId = null;
            ws.playerSide = null;
        }
    }

    broadcast(message, excludeWs = null) {
        this.players.forEach(player => {
            if (player !== excludeWs && player.readyState === WebSocket.OPEN) {
                player.send(JSON.stringify(message));
            }
        });
    }

    updateGameState(newState, fromWs) {
        this.gameState = { ...this.gameState, ...newState };

        // 广播游戏状态更新给其他玩家
        this.broadcast({
            type: 'gameStateUpdate',
            gameState: this.gameState
        }, fromWs);
    }
}

// 广播用户列表给所有连接的用户
function broadcastUserList() {
    const userList = Array.from(connectedUsers.values()).map(user => {
        let opponentName = null;
        if (user.inGame && user.roomId && gameRooms.has(user.roomId)) {
            const room = gameRooms.get(user.roomId);
            const opponent = room.getOpponent(user);
            if (opponent) {
                opponentName = opponent.playerName;
            }
        }

        return {
            playerId: user.playerId,
            playerName: user.playerName,
            inGame: user.inGame || false,
            opponentName: opponentName
        };
    });

    const message = JSON.stringify({
        type: 'userListUpdate',
        users: userList
    });

    connectedUsers.forEach(user => {
        if (user.readyState === WebSocket.OPEN) {
            user.send(message);
        }
    });
}

// WebSocket连接处理
wss.on('connection', (ws) => {
    ws.on('message', (message) => {
        try {
            const data = JSON.parse(message);

            switch (data.type) {
                case 'joinLobby':
                    // 用户加入大厅
                    ws.playerId = getNextAvailableUserId();
                    ws.playerName = `玩家${ws.playerId}`;
                    ws.inGame = false;
                    ws.roomId = null;
                    ws.playerSide = null;

                    connectedUsers.set(ws.playerId, ws);

                    // 发送用户信息
                    ws.send(JSON.stringify({
                        type: 'lobbyJoined',
                        playerId: ws.playerId,
                        playerName: ws.playerName
                    }));

                    // 广播更新的用户列表
                    broadcastUserList();
                    break;

                case 'challengeUser':
                    // 发送挑战邀请
                    const targetUser = connectedUsers.get(data.targetUserId);
                    if (targetUser && targetUser.readyState === WebSocket.OPEN && !targetUser.inGame) {
                        targetUser.send(JSON.stringify({
                            type: 'challengeReceived',
                            fromUserId: ws.playerId,
                            fromUserName: ws.playerName
                        }));

                        ws.send(JSON.stringify({
                            type: 'challengeSent',
                            message: `已向 ${targetUser.playerName} 发送挑战邀请`
                        }));
                    } else {
                        ws.send(JSON.stringify({
                            type: 'error',
                            message: '用户不可用或正在游戏中'
                        }));
                    }
                    break;

                case 'challengeResponse':
                    // 响应挑战邀请
                    const challenger = connectedUsers.get(data.fromUserId);
                    if (challenger && challenger.readyState === WebSocket.OPEN) {
                        if (data.accepted) {
                            // 接受挑战，创建游戏房间
                            const roomId = 'room_' + Date.now();
                            const room = new GameRoom(roomId, challenger, ws);
                            gameRooms.set(roomId, room);

                            // 广播更新的用户列表
                            broadcastUserList();
                        } else {
                            // 拒绝挑战
                            challenger.send(JSON.stringify({
                                type: 'challengeDeclined',
                                message: `${ws.playerName} 拒绝了您的挑战`
                            }));
                        }
                    }
                    break;

                case 'gameStateUpdate':
                    // 更新游戏状态
                    if (ws.roomId && gameRooms.has(ws.roomId)) {
                        const room = gameRooms.get(ws.roomId);
                        room.updateGameState(data.gameState, ws);
                    }
                    break;

                case 'move':
                    // 处理移动
                    if (ws.roomId && gameRooms.has(ws.roomId)) {
                        const room = gameRooms.get(ws.roomId);
                        room.broadcast({
                            type: 'move',
                            move: data.move,
                            playerId: ws.playerId
                        }, ws);
                    }
                    break;

                case 'selection':
                    // 处理选择状态
                    if (ws.roomId && gameRooms.has(ws.roomId)) {
                        const room = gameRooms.get(ws.roomId);
                        room.broadcast({
                            type: 'selection',
                            selection: data.selection,
                            playerId: ws.playerId
                        }, ws);
                    }
                    break;

                case 'ping':
                    // 心跳响应
                    ws.send(JSON.stringify({ type: 'pong' }));
                    break;
            }
        } catch (error) {
            ws.send(JSON.stringify({
                type: 'error',
                message: '服务器错误'
            }));
        }
    });

    ws.on('close', () => {
        // 从连接用户列表中移除
        if (ws.playerId && connectedUsers.has(ws.playerId)) {
            connectedUsers.delete(ws.playerId);
            // 释放用户ID供后续用户使用
            releaseUserId(ws.playerId);
            broadcastUserList();
        }

        // 从房间中移除玩家
        if (ws.roomId && gameRooms.has(ws.roomId)) {
            const room = gameRooms.get(ws.roomId);
            room.removePlayer(ws);

            // 删除房间
            gameRooms.delete(ws.roomId);

            // 广播更新的用户列表
            broadcastUserList();
        }
    });

    ws.on('error', (error) => {
        // 静默处理错误
    });
});

const PORT = process.env.PORT || 3002;

// 获取本机IP地址
function getLocalIP() {
    const os = require('os');
    const interfaces = os.networkInterfaces();
    for (const name of Object.keys(interfaces)) {
        for (const interface of interfaces[name]) {
            if (interface.family === 'IPv4' && !interface.internal) {
                return interface.address;
            }
        }
    }
    return 'localhost';
}

server.listen(PORT, '0.0.0.0', () => {
    const localIP = getLocalIP();
    console.log('='.repeat(60));
    console.log('🎮 中国象棋联机服务器已启动');
    console.log('='.repeat(60));
    console.log(`📱 本机访问: http://localhost:${PORT}`);
    console.log(`🌐 局域网访问: http://${localIP}:${PORT}`);

    // 如果在GitHub Codespaces环境中
    if (process.env.CODESPACE_NAME) {
        console.log(`☁️  GitHub Codespaces访问: https://${process.env.CODESPACE_NAME}-${PORT}.preview.app.github.dev`);
        console.log(`🔗 WebSocket地址: wss://${process.env.CODESPACE_NAME}-${PORT}.preview.app.github.dev`);
    }

    console.log('='.repeat(60));
    console.log('💡 使用说明:');
    console.log('   • 单人游戏: 直接在浏览器中游戏');
    console.log('   • 局域网联机: 两个玩家访问同一地址');
    console.log('   • 远程联机: 使用GitHub Codespaces地址');
    console.log('='.repeat(60));
    console.log('⚠️  关闭服务器: 按 Ctrl+C 或在终端中输入 Ctrl+C');
    console.log('='.repeat(60));
});

// 优雅关闭服务器
process.on('SIGINT', () => {
    console.log('\n' + '='.repeat(60));
    console.log('🛑 正在关闭服务器...');
    console.log('='.repeat(60));

    // 关闭WebSocket服务器
    wss.close(() => {
        console.log('✅ WebSocket服务器已关闭');
    });

    // 关闭HTTP服务器
    server.close(() => {
        console.log('✅ HTTP服务器已关闭');
        console.log('👋 服务器已安全关闭，感谢使用！');
        console.log('='.repeat(60));
        process.exit(0);
    });
});

process.on('SIGTERM', () => {
    console.log('\n' + '='.repeat(60));
    console.log('🛑 收到终止信号，正在关闭服务器...');
    console.log('='.repeat(60));

    wss.close(() => {
        console.log('✅ WebSocket服务器已关闭');
    });

    server.close(() => {
        console.log('✅ HTTP服务器已关闭');
        console.log('👋 服务器已安全关闭！');
        console.log('='.repeat(60));
        process.exit(0);
    });
});
