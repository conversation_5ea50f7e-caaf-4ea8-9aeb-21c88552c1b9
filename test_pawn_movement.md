# 兵的移动规则测试

## 功能说明

### 一、铁路线移动（快速路径）
- **铁路线定义**: 0、4、5、9行和0、8列
- **移动规则**: 无其他棋子阻挡时，可在铁路线上任意方向移动
- **支持移动**: 直走、直角拐弯
- **移动步数**: 不受限制

### 二、公路线移动（常规路径）
- **移动规则**: 每次只能移动1格
- **移动方向**: 仅限上下左右直走

### 三、棋盘坐标
- 每个位置显示坐标 (列,行)
- 坐标范围: 列 0-8, 行 0-9

## 测试步骤

1. 启动游戏，选择"单人游戏"
2. 初始化阶段：将兵放置在不同位置进行测试
3. 开始游戏后测试兵的移动

### 测试用例

#### 铁路线移动测试
1. **直线移动**: 兵在(0,0)移动到(0,9) - 应该成功
2. **水平移动**: 兵在(0,4)移动到(8,4) - 应该成功
3. **单次拐弯**: 兵在(0,0)移动到(8,4) - 应该成功（通过拐弯点）
4. **多次拐弯**: 兵在(8,7)移动到(0,1) - 应该成功（路径：(8,7)→(8,5)→(0,5)→(0,1)）
5. **复杂路径**: 兵在(8,9)移动到(0,0) - 应该成功（多种可能路径）
6. **被阻挡**: 在路径上放置其他棋子，移动应该失败

#### 公路线移动测试
1. **单格移动**: 兵在(1,1)移动到(1,2) - 应该成功
2. **单格移动**: 兵在(1,1)移动到(2,1) - 应该成功
3. **多格移动**: 兵在(1,1)移动到(1,3) - 应该失败
4. **斜向移动**: 兵在(1,1)移动到(2,2) - 应该失败

#### 混合移动测试
1. **铁路到公路**: 兵在(0,0)移动到(1,1) - 应该失败（超过1格且不在铁路线）
2. **公路到铁路**: 兵在(1,1)移动到(0,1) - 应该成功（1格移动）

## 预期结果

- 兵可以在铁路线上快速移动
- 兵在非铁路线位置只能移动1格
- 坐标正确显示在每个位置
- 移动规则符合设计要求
